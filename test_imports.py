#!/usr/bin/env python3
"""
Test script to verify all imports work correctly for the streamlit app.
This simulates the container environment to identify missing dependencies.
"""

import sys
import os

# Add src to path (simulating container environment)
sys.path.insert(0, 'src')

def test_import(module_name, description=""):
    """Test importing a module and report results."""
    try:
        if module_name == "agents.cv_extractor":
            from agents.cv_extractor import process_cv_extraction
            print(f"✅ SUCCESS: {module_name} - {description}")
            return True
        elif module_name == "client":
            from client import AgentClient, AgentClientError
            print(f"✅ SUCCESS: {module_name} - {description}")
            return True
        elif module_name == "schema":
            from schema import ChatHistory, ChatMessage
            from schema.task_data import TaskData, TaskDataStatus
            print(f"✅ SUCCESS: {module_name} - {description}")
            return True
        elif module_name == "core":
            from core import get_model
            from core.settings import settings
            print(f"✅ SUCCESS: {module_name} - {description}")
            return True
        elif module_name == "agents.embedded_people_skills":
            from agents.embedded_people_skills import ResumeDB, upsert_resume_chunks
            print(f"✅ SUCCESS: {module_name} - {description}")
            return True
        elif module_name == "agents.prompt_lib":
            from agents.prompt_lib import EXTRACTOR_AGENT_INSTRUCTIONS, JSON_OUTPUT_FORMAT
            print(f"✅ SUCCESS: {module_name} - {description}")
            return True
        else:
            exec(f"import {module_name}")
            print(f"✅ SUCCESS: {module_name} - {description}")
            return True
    except ImportError as e:
        print(f"❌ IMPORT ERROR: {module_name} - {e}")
        return False
    except Exception as e:
        print(f"⚠️  OTHER ERROR: {module_name} - {e}")
        return True  # Other errors mean import worked but runtime issue

def main():
    """Test all critical imports for the streamlit app."""
    print("Testing imports for Streamlit app container...")
    print("=" * 60)
    
    # Test standard library imports
    test_import("asyncio", "Standard library")
    test_import("os", "Standard library")
    test_import("urllib.parse", "Standard library")
    test_import("uuid", "Standard library")
    test_import("io", "Standard library")
    
    # Test third-party dependencies
    test_import("pandas", "Data manipulation")
    test_import("streamlit", "Web app framework")
    test_import("dotenv", "Environment variables")
    test_import("pydantic", "Data validation")
    test_import("pypdf", "PDF processing")
    
    # Test langchain dependencies
    test_import("langchain_core.messages", "LangChain core")
    test_import("langchain_core.prompts", "LangChain prompts")
    test_import("langchain_openai", "LangChain OpenAI")
    test_import("langgraph.checkpoint.memory", "LangGraph memory")
    test_import("langgraph.prebuilt", "LangGraph prebuilt")
    
    # Test database dependencies
    test_import("psycopg2", "PostgreSQL adapter")
    test_import("openai", "OpenAI API")
    
    # Test local modules
    test_import("client", "Local client module")
    test_import("schema", "Local schema module")
    test_import("core", "Local core module")
    test_import("agents.prompt_lib", "Local prompt library")
    test_import("agents.embedded_people_skills", "Local embedded skills")
    test_import("agents.cv_extractor", "Local CV extractor")
    
    print("=" * 60)
    print("Import testing completed!")

if __name__ == "__main__":
    main()
